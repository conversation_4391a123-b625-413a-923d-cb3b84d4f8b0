﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableWindowsTargeting>true</EnableWindowsTargeting>
    <OutputPath>..\..\Mis.Agent\bin\Debug\net9.0-windows\Plugins\</OutputPath>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Cors" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
    <PackageReference Include="PdfiumViewer.Core" Version="1.0.4" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />


  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Mis.Shared.Interface\Mis.Shared.Interface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="IronPdf.Core">
      <HintPath>\IronPdf.Core\IronPdf.Core.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\PrintResources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PrintResources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\PrintResources.ar-SA.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <CustomToolNamespace>Mis.Agent.Print.Resources</CustomToolNamespace>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\PrintResources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>PrintResources.Designer.cs</LastGenOutput>
      <CustomToolNamespace>Mis.Agent.Print.Resources</CustomToolNamespace>
    </EmbeddedResource>
  </ItemGroup>
</Project>