﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Mis.Agent.Print
{
    [Plugin("Notifications", Version = "1.0.0", Description = "Notifications management", Order = 40)]

    public class NotificationsService : INotificationAppService
    {
        private string _databaseFile;
        private bool _notificationsEnabled;
        private readonly object _lock = new object();
        private EntitiesAppsettings _settings;
        public event Action NotificationUpdated;
        public NotificationsService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");
            EnsureDatabaseAndTables();
            NotificationManager.NotificationEvent += OnNotificationReceived;

            // Load settings using IConfiguration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            _settings = new EntitiesAppsettings
            {
                NotificationSettings = configuration.GetSection("NotificationSettings").Get<NotificationSettings>()
                    ?? new NotificationSettings
                    {
                        EnabeledNotification = false,
                        NotificationCleanup = new NotificationCleanupSettings
                        {
                            Enabled = false,
                            KeepDays = null,
                            MaxRecords = null
                        }
                    }
            };

            _notificationsEnabled = _settings.NotificationSettings?.EnabeledNotification ?? false;
        }

        public Task<NotificationSettings> GetNotificationSettingsAsync()
        {
            return Task.FromResult(_settings?.NotificationSettings ?? new NotificationSettings());
        }






        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        [PluginNotificationHandler]

        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        [PluginTabProvider]

        public object GetTabPage()
        {
            var notificationsForm = new NotificationsForm(this);
            notificationsForm.PopulateForm();
            return notificationsForm.NotificationsTab;
        }
        private void EnsureDatabaseAndTables()
        {

            if (!File.Exists(_databaseFile))
            {
                // Create the database file
                SQLiteConnection.CreateFile(_databaseFile);
            }

            // Create tables if they don't exist
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();

                var createNotificationsTableQuery = @"
                CREATE TABLE IF NOT EXISTS Notifications (
                    Id TEXT PRIMARY KEY,
                    No TEXT NOT NULL,
                    HtmlContent TEXT,
                    IsPrinted INTEGER NOT NULL,
                    ReceiveTime DATETIME NOT NULL
                )";

                using (var command = new SQLiteCommand(createNotificationsTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public bool DatabaseFileExists() => File.Exists(_databaseFile);

        public string GetDatabasePath() => _databaseFile;



        public bool TableExists(string tableName)
        {
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();
                var query = $"SELECT name FROM sqlite_master WHERE type='table' AND name='{tableName}';";

                using (var command = new SQLiteCommand(query, connection))
                {
                    var result = command.ExecuteScalar();
                    return result != null && result.ToString() == tableName;
                }
            }
        }



        public async Task PerformCleanupAsync()
        {
            var notificationCleanup = _settings.NotificationSettings.NotificationCleanup;
            if (!notificationCleanup.Enabled)
                return;

            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                await connection.OpenAsync();

                if (notificationCleanup.KeepDays.HasValue)
                {
                    var cutoff = DateTime.Now.AddDays(-notificationCleanup.KeepDays.Value);
                    var deleteOld = "DELETE FROM Notifications WHERE ReceiveTime < @CutoffDate";

                    using var cmd = new SQLiteCommand(deleteOld, connection);
                    cmd.Parameters.AddWithValue("@CutoffDate", cutoff);
                    await cmd.ExecuteNonQueryAsync();
                }

                if (notificationCleanup.MaxRecords.HasValue)
                {
                    var deleteExtra = @"
                DELETE FROM Notifications
                WHERE Id NOT IN (
                    SELECT Id FROM Notifications
                    ORDER BY ReceiveTime DESC
                    LIMIT @MaxRecords
                );";

                    using var cmd = new SQLiteCommand(deleteExtra, connection);
                    cmd.Parameters.AddWithValue("@MaxRecords", notificationCleanup.MaxRecords.Value);
                    await cmd.ExecuteNonQueryAsync();
                }
            }
            NotificationManager.NotifyDataUpdated();

        }

        public async Task ClearAllNotificationsAsync()
        {
            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        using (var connection = new SQLiteConnection($"Data Source={_databaseFile}"))
                        {
                            connection.Open();

                            // Clear all notifications from the table
                            var command = new SQLiteCommand("DELETE FROM notifications", connection);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error or handle as needed
                        throw new InvalidOperationException($"Failed to clear notifications: {ex.Message}", ex);
                    }
                }
            });
            NotificationManager.NotifyDataUpdated();
        }

        #region Notifications

        public async Task<IEnumerable<TransactionDto>> GetAllNotificationsAsync()
        {
            var notifications = new List<TransactionDto>();

            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();
                var query = "SELECT Id, No, HtmlContent, IsPrinted, ReceiveTime FROM Notifications";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        notifications.Add(new TransactionDto
                        {
                            Id = Guid.Parse(reader["Id"].ToString()),
                            No = reader["No"].ToString(),
                            HtmlContent = reader["HtmlContent"].ToString(),
                            IsPrinted = Convert.ToBoolean(reader["IsPrinted"]),
                            ReceiveTime = Convert.ToDateTime(reader["ReceiveTime"])
                        });
                    }
                }
            }

            return notifications;
        }

        public async Task SaveNotificationAsync(TransactionDto input)
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    // Check if the record with the given Id exists
                    var checkQuery = "SELECT COUNT(1) FROM Notifications WHERE Id = @Id";
                    using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

                        if (exists)
                        {
                            // Record exists, so update it
                            var updateQuery = @"
                        UPDATE Notifications
                        SET No = @No, HtmlContent = @HtmlContent, IsPrinted = @IsPrinted, ReceiveTime = @ReceiveTime
                        WHERE Id = @Id";

                            using (var updateCommand = new SQLiteCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                                updateCommand.Parameters.AddWithValue("@No", input.No);
                                updateCommand.Parameters.AddWithValue("@HtmlContent", input.HtmlContent);
                                updateCommand.Parameters.AddWithValue("@IsPrinted", input.IsPrinted ? 1 : 0);
                                updateCommand.Parameters.AddWithValue("@ReceiveTime", DateTime.Now);

                                int rowsAffected = await updateCommand.ExecuteNonQueryAsync();
                                Console.WriteLine($"{rowsAffected} row(s) updated.");
                            }
                        }
                        else
                        {
                            // Record doesn't exist, so insert a new one
                            var insertQuery = @"
                        INSERT INTO Notifications (Id, No, HtmlContent, IsPrinted, ReceiveTime)
                        VALUES (@Id, @No, @HtmlContent, @IsPrinted, @ReceiveTime)";

                            using (var insertCommand = new SQLiteCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@Id", input.Id.ToString());
                                insertCommand.Parameters.AddWithValue("@No", input.No);
                                insertCommand.Parameters.AddWithValue("@HtmlContent", input.HtmlContent);
                                insertCommand.Parameters.AddWithValue("@IsPrinted", input.IsPrinted ? 1 : 0);
                                insertCommand.Parameters.AddWithValue("@ReceiveTime", DateTime.Now);

                                int rowsAffected = await insertCommand.ExecuteNonQueryAsync();
                                Console.WriteLine($"{rowsAffected} row(s) inserted.");
                            }
                        }

                        NotificationManager.NotifyDataUpdated();
                    }
                }
            }
            catch (SQLiteException ex)
            {
                // Handle SQLite-specific exceptions
                Console.WriteLine($"SQLite error saving notification: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                Console.WriteLine($"Error saving notification: {ex.Message}");
            }
        }

        public async Task UpdatePrintStatusAsync(Guid transactionId, bool isPrinted)
        {
            try
            {
                using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
                {
                    await connection.OpenAsync();

                    string updateQuery = "UPDATE Notifications SET IsPrinted = @IsPrinted WHERE Id = @Id";
                    using (var command = new SQLiteCommand(updateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Id", transactionId.ToString());
                        command.Parameters.AddWithValue("@IsPrinted", isPrinted ? 1 : 0);

                        int rowsAffected = await command.ExecuteNonQueryAsync();
                        Console.WriteLine($"{rowsAffected} row(s) updated.");
                    }
                    NotificationManager.NotifyDataUpdated();
                }
            }
            catch (SQLiteException ex)
            {
                // Handle SQLite-specific exceptions
                Console.WriteLine($"SQLite error updating print status: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle general exceptions
                Console.WriteLine($"Error updating print status: {ex.Message}");
            }
        }

        #endregion
    }
}
