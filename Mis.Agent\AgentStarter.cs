using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Mis.Agent;
using Mis.Agent.PluginSystem;
using Mis.Agent.Print;
using Mis.Shared.Interface;
using System;
using System.Configuration;
using System.Data;
using System.IO.Ports;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using TransactionDto = Mis.Shared.Interface.TransactionDto;

namespace Mis.Agent.ApplicationsContext
{
    public class AgentStarter : ApplicationContext
    {
        private IHost _webHost;
        private NotifyIcon trayIcon;
        private TransactionDto? _transactionDto;
        private AgentForm? _agentForm;
        IServiceProvider _serviceProvider;
        public AgentStarter(IServiceProvider serviceProvider)
        {

            _serviceProvider = serviceProvider;
            SerialPortManager.Instance.BarcodeImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.scannerImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.NotificationUpdated += OnNotificationUpdated;

            InitializeTrayIcon();
        }


        /// <summary>
        /// Initialize plugins once on application startup
        /// </summary>

        private void OnPluginLoadedStartup(object? sender, PluginLoadedEventArgs e)
        {
            Console.WriteLine($"Plugin loaded on startup: {e.Plugin.Name} v{e.Plugin.Version}");
        }

        private void OnPluginLoadFailedStartup(object? sender, PluginLoadFailedEventArgs e)
        {
            Console.WriteLine($"Plugin failed to load on startup: {e.Exception.Message}");
        }
        /// <summary>
        /// Initialize barcode service to start listening for barcode data
        /// </summary>
        private async Task InitializeBarcodeService()
        {
            try
            {
                var pluginManager = PluginManager.Instance;
                var barcodePlugin = pluginManager.LoadedPlugins.FirstOrDefault(p => p.Name.Contains("Barcode"));

                if (barcodePlugin != null)
                {
                    // Initialize barcode service to start listening
                    var initMethod = barcodePlugin.GetType().GetMethod("Initialize");
                    if (initMethod != null)
                    {
                        initMethod.Invoke(barcodePlugin, null);
                        Console.WriteLine("Barcode service initialized and listening.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing barcode service: {ex.Message}");
            }
        }



        private void InitializeTrayIcon()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.AddRange(new ToolStripItem[] {
            new ToolStripMenuItem("Show Tabs Form", null, ShowTabsForm),
            new ToolStripMenuItem("Exit", null, Exit)
        });

            trayIcon = new NotifyIcon
            {
                Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath),
                ContextMenuStrip = contextMenu,
                Visible = true,
                Text = "Agent Application"
            };

            trayIcon.MouseClick += TrayIcon_MouseClick;
        }



        private async void TrayIcon_MouseClick(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                await ShowOrRefreshTabsFormAsync();
            }
        }

        private async void ShowTabsForm(object? sender, EventArgs e)
        {
            try
            {
                bool result = await ShowOrRefreshTabsFormAsync();
                MessageBox.Show(result ? "TabsForm is already opened." : "TabsForm operation failed.");
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in ShowTabsForm: {ex.Message}");
            }
        }

        public async Task<bool> ShowOrRefreshTabsFormAsync()
        {
            try
            {
                if (_agentForm == null || _agentForm.IsDisposed)
                {
                    return await RunTabsFormAsync(_transactionDto);
                }
                else
                {
                    if (_agentForm.InvokeRequired)
                    {
                        _agentForm.Invoke(new Action(() =>
                        {
                            _agentForm.BringToFront();
                            _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                        }));
                    }
                    else
                    {
                        _agentForm.BringToFront();
                        _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in ShowOrRefreshTabsFormAsync: {ex.Message}");

                return true;
            }

        }

        private async Task<bool> RunTabsFormAsync(TransactionDto? transactionDto)
        {
            try
            {
                var taskCompletionSource = new TaskCompletionSource<bool>();

                var transactionFormThread = new Thread(() =>
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        _agentForm = new AgentForm(_serviceProvider)
                        {
                            StartPosition = FormStartPosition.CenterScreen
                        };
                        Application.Run(_agentForm);
                        taskCompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        taskCompletionSource.SetException(ex);
                    }
                });

                transactionFormThread.SetApartmentState(ApartmentState.STA);
                transactionFormThread.Start();

                return await taskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in RunTabsFormAsync: {ex.Message}");
                return false;
            }

        }


        private void Exit(object? sender, EventArgs e)
        {
            try
            {
                trayIcon.Visible = false;
                Application.Exit();
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in Exit method: {ex.Message}");
            }
        }
        private void OnImageCaptured(Image capturedImage)
        {
            try
            {
                // Access the ScannerTab by its name
                var scannerTab = _agentForm?.tabControl1.TabPages["ScannerTab"] as TabPage;
                if (scannerTab != null)
                {
                    var pictureBox = scannerTab.Controls["pictureScanned"] as PictureBox;
                    if (pictureBox != null)
                    {
                        pictureBox.Image = capturedImage;
                    }
                    else
                    {
                        MessageBox.Show("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void OnNotificationUpdated()
        {
            RefreshDataGridView();
        }
        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = _agentForm?.tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}