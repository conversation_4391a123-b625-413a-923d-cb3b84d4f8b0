﻿using IronPdf;
using Microsoft.Extensions.Configuration;
using Mis.Agent.Print;
using Mis.Agent.Print.Resources;
using Mis.Shared.Interface;
using Newtonsoft.Json;
using PdfiumViewer;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Drawing.Printing;
using System.Linq;
using System.Runtime;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Print
{
    [Plugin("Print & Notifications", Version = "1.0.0", Description = "Printing and notifications management", Order = 30)]

    public class PrintService : IPrintAppService
    {
        public PrintDocument PrintDocument { get; private set; }
        PrintForm printForm;
        private readonly IConfiguration _configuration;
        PrintJobMonitor printJobMonitor;
        HtmlToPdf? renderer;
        private bool _notificationsEnabled ;
        private TaskCompletionSource<bool> _taskCompletionSource;
        private readonly Dictionary<string, object> _settings;
        private readonly string _appsettingsFilePath;
        public event Action<bool> NotificationStateChanged;

        public PrintService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            var appsettingsFilePath = Path.Combine(baseDirectory, "appsettings.json");
            if (!File.Exists(appsettingsFilePath))
            {
                MessageBox.Show($"Missing configuration: {appsettingsFilePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }
            _appsettingsFilePath = appsettingsFilePath;

            _configuration = new ConfigurationBuilder()
                .SetBasePath(baseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            renderer = new HtmlToPdf();
            printJobMonitor = new PrintJobMonitor();
            PrintDocument = new PrintDocument();
            printForm = new PrintForm(this);
            _taskCompletionSource = new TaskCompletionSource<bool>();
            _settings = SerialPortManager.Instance.LoadSettingsFromAppsettings();
            NotificationManager.NotificationEvent += OnNotificationReceived;
            _notificationsEnabled = SerialPortManager.Instance.GetNotificationEnabledSetting() ?? false;

        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
     

        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        private Dictionary<string, string> LoadSettings()
        {
            try
            {
                var settings = new Dictionary<string, string>();

                // Load the PrinterEntity section
                var printerEntity = _configuration.GetSection("PrinterEntity").Get<PrinterEntity>();
                if (printerEntity != null)
                {
                    settings["DefaultPrinter"] = printerEntity.DefaultPrinter;
                    settings["DefaultPaperSize"] = printerEntity.DefaultPaperSize;
                }

                return settings;
            }
            catch (Exception ex)
            {
                throw new Exception("Error loading settings", ex);
            }
        }
        public PrinterEntity GetPrinterEntity()
        {
            return _configuration.GetSection("PrinterEntity").Get<PrinterEntity>();
        }

    

   
        [PluginTabProvider]
        public object GetTabPage()
        {
            printForm.PopulateForm(); // Populate form elements

            return printForm.PrintTab; // Return the TabPage as an object
        }

        public bool PrintAsync(TransactionDto transactionDto)
        {
            try
            {
                // Get the printer settings from SettingsManager
                PrinterEntity printerSettings = GetPrinterEntity();
                string defaultPrinter = printerSettings.DefaultPrinter;
                string defaultPaperSize = printerSettings.DefaultPaperSize;

                // Set up the printer and paper size
                var paperSize = ConfigurePrinterSettings(defaultPrinter, defaultPaperSize);

                if (!string.IsNullOrEmpty(transactionDto?.HtmlContent))
                {
                    // Generate the PDF file from HTML content
                    string tempFilePath = GeneratePdfFile(transactionDto.HtmlContent, paperSize);

                    // Print the PDF file and delete after printing
                    return PrintPdfAndMonitorJobAsync(tempFilePath, defaultPrinter, paperSize);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return HandlePrintingError(ex);
            }
        }







        public void SetPrinter(string printerName)
        {
            PrintDocument.PrinterSettings.PrinterName = printerName;
        }

        public void SetPaperSize(PaperSize paperSize)
        {
            PrintDocument.DefaultPageSettings.PaperSize = paperSize;
        }
        public string GetDefaultPrinter()
        {
            // Create a PrinterSettings object which automatically picks up the default printer
            PrinterSettings settings = new PrinterSettings();

            // Return the name of the default printer
            return settings.PrinterName;
        }

        public IEnumerable<string> GetInstalledPrinters()
        {
            return PrinterSettings.InstalledPrinters.Cast<string>();
        }

        public PaperSize[] GetPaperSizes(string printerName)
        {
            using (var tempPrintDocument = new PrintDocument())
            {
                tempPrintDocument.PrinterSettings.PrinterName = printerName;
                return tempPrintDocument.PrinterSettings.PaperSizes.Cast<PaperSize>().ToArray();
            }
        }






        public bool PrintPdf(string pdfFilePath, string printerName, PaperSize paperSize)
        {
            try
            {
                using (var pdfDocument = PdfiumViewer.PdfDocument.Load(pdfFilePath))
                {
                    // Create a PrintDocument from PdfiumViewer
                    var printDocument = pdfDocument.CreatePrintDocument();
                    // Set page settings
                    printDocument.DefaultPageSettings = new PageSettings
                    {
                        PaperSize = paperSize,
                        //Landscape = true // Set to true for landscape orientation
                    };
                    printDocument.PrinterSettings.PrinterName = printerName;


                    // Print the document
                    printDocument.Print();

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error printing PDF: {ex.Message}");
                return false;
            }
        }







        private PaperSize ConfigurePrinterSettings(string printerName, string paperSizeName)
        {
            var paperSizes = GetPaperSizes(printerName).ToList();

            if (!string.IsNullOrEmpty(paperSizeName))
            {
                var selectedPaperSize = paperSizes.FirstOrDefault(p => p.PaperName.Equals(paperSizeName, StringComparison.OrdinalIgnoreCase));

                if (selectedPaperSize != null)
                {
                    SetPaperSize(selectedPaperSize);
                    return selectedPaperSize;
                }
                else
                {
                    Console.WriteLine("Default paper size not found. Using the first available paper size.");
                }
            }
            else
            {
                Console.WriteLine("Default paper size is not set in settings.");
            }

            // Fallback to the first available paper size if the specified one is not found
            var fallbackPaperSize = paperSizes.FirstOrDefault();
            if (fallbackPaperSize != null)
            {
                SetPaperSize(fallbackPaperSize);
            }

            return fallbackPaperSize;
        }

        private string GeneratePdfFile(string htmlContent, PaperSize paperSize)
        {
            try
            {

                // Get localized strings from .resx
                string title = PrintResources.PrintingError;
                string message = PrintResources.HTMLContent;



                if (string.IsNullOrWhiteSpace(htmlContent))
                {
                    ShowNotification(title, message);
                    throw new ArgumentException(message);
                }
                string tempFilePath = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString() + ".pdf");

                IronPdf.License.LicenseKey = "IRONPDF-BOARD4ALL.BIZ-120981-6E5672-8683679C43-3138BC52-NEx-T24";

                var printOptions = new PdfPrintOptions
                {
                    InputEncoding = Encoding.UTF8,
                    FitToPaperWidth = true,
                    Zoom = 400,
                    MarginLeft = 0,
                    MarginRight = 0,
                    MarginTop = 0,
                    MarginBottom = 10,
                };

                if (paperSize != null)
                {
                    double widthInMm = paperSize.Width;
                    double heightInMm = paperSize.Height;
                    printOptions.SetCustomPaperSizeinMilimeters(widthInMm, heightInMm);
                }

                var renderer = new HtmlToPdf(printOptions);

                var pdfDocument = renderer.RenderHtmlAsPdf(htmlContent);
                File.WriteAllBytes(tempFilePath, pdfDocument.BinaryData);

                return tempFilePath;
            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }

        }

        private bool PrintPdfAndMonitorJobAsync(string filePath, string printerName, PaperSize paperSize)
        {
            bool printSuccess = false;

            try
            {
                printSuccess = PrintPdf(filePath, printerName, paperSize);

                if (printSuccess)
                {
                    return printJobMonitor.MonitorPrintJobAsync(printerName);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
                throw; // Re-throw the exception after setting the task result
            }
            finally
            {
                // Ensure the file is deleted whether the print succeeds or fails
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        private bool HandlePrintingError(Exception ex)
        {
            Console.WriteLine($"An error occurred during printing: {ex.Message}");
            return false;
        }







    }
}
