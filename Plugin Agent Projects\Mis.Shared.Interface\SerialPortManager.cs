﻿using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Configuration;
using Mis.Shared.Interface;
using System;
using System.Data.SQLite;
using System.Drawing;
using System.IO.Ports;
using System.Runtime;
using System.Text;
using WIA;

namespace Mis.Shared.Interface
{
    public class SerialPortManager
    {
        private static readonly SerialPortManager _instance = new SerialPortManager();
        private readonly IConfiguration _configuration;
        public SerialPort _serialPort;
        private SerialPortManager()
        {

            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            _configuration = new ConfigurationBuilder()
             .SetBasePath(AppContext.BaseDirectory)
             .AddJsonFile("appsettings.json", optional: true)
             .Build();

        }
        public bool? GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            return section.GetValue<bool?>("EnabeledNotification");
        }
        public Dictionary<string, object> LoadSettingsFromAppsettings()
        {
            var snapshot = new Dictionary<string, object>();

            try
            {
                AddSectionToSnapshot(snapshot, "PrinterEntity", "PrintTab", "DefaultPrinter", "DefaultPaperSize");
                AddSectionToSnapshot(snapshot, "Server", "PortTab", ("BaseUrl", true));
                AddSectionToSnapshot(snapshot, "Barcode", "BarcodeTab", "BarcodeBaseUrl", "ComPort");
                AddSectionToSnapshot(snapshot, "DefaultScanner", "ScannerTab", "Scanner", "IsScanByBarcodeReader");

                // Handle NotificationSettings (with nested cleanup)
                var notificationsSection = _configuration.GetSection("NotificationSettings");
                if (notificationsSection.Exists())
                {
                    var cleanupSection = notificationsSection.GetSection("NotificationCleanup");
                    var cleanupDict = new Dictionary<string, object>
            {
                { "Enabled", cleanupSection.GetValue<bool>("Enabled") },
                { "MaxRecords", cleanupSection.GetValue<int>("MaxRecords") },
                { "KeepDays", cleanupSection.GetValue<int>("KeepDays") }
            };

                    snapshot["NotificationsTab"] = new Dictionary<string, object>
            {
                { "EnabeledNotification", notificationsSection.GetValue<bool>("EnabeledNotification") },
                { "NotificationCleanup", cleanupDict }
            };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error retrieving settings snapshot: {ex.Message}");
            }

            return snapshot;
        }
        private void AddSectionToSnapshot(Dictionary<string, object> snapshot, string sectionName, string tabKey, params object[] keys)
        {
            var section = _configuration.GetSection(sectionName);
            if (!section.Exists())
                return;

            var tabDict = new Dictionary<string, object>();

            foreach (var key in keys)
            {
                if (key is string strKey)
                {
                    tabDict[strKey] = section.GetValue<object>(strKey);
                }
                else if (key is ValueTuple<string, bool> specialKey)
                {
                    tabDict[specialKey.Item1] = section.GetValue<object>(specialKey.Item1);
                    if (specialKey.Item2)
                    {
                        tabDict["RequiresRestart"] = true;
                    }
                }
            }

            snapshot[tabKey] = tabDict;
        }
        public static SerialPortManager Instance => _instance;
        public bool IsPortOpen => _serialPort != null && _serialPort.IsOpen;
    












    }
}