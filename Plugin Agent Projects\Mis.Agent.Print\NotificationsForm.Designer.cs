﻿namespace Mis.Agent.Print
{
    partial class NotificationsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            NotificationsTab = new TabPage();
            enableCleanupCheckBox = new CheckBox();
            txtMaxRecords = new TextBox();
            txtKeepDays = new TextBox();
            label2 = new Label();
            label1 = new Label();
            enableNotificationsCheckBox = new CheckBox();
            dataGridView1 = new DataGridView();
            groupBox1 = new GroupBox();
            tabControl1.SuspendLayout();
            NotificationsTab.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(NotificationsTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(1020, 628);
            tabControl1.TabIndex = 0;
            // 
            // NotificationsTab
            // 
            NotificationsTab.Controls.Add(groupBox1);
            NotificationsTab.Controls.Add(enableNotificationsCheckBox);
            NotificationsTab.Controls.Add(dataGridView1);
            NotificationsTab.Location = new Point(4, 29);
            NotificationsTab.Name = "NotificationsTab";
            NotificationsTab.Padding = new Padding(3);
            NotificationsTab.Size = new Size(1012, 595);
            NotificationsTab.TabIndex = 0;
            NotificationsTab.Text = "Notifications Tab";
            NotificationsTab.UseVisualStyleBackColor = true;
            // 
            // checkBox2
            // 
            enableCleanupCheckBox.AutoSize = true;
            enableCleanupCheckBox.Location = new Point(13, 46);
            enableCleanupCheckBox.Name = "enableCleanupCheckBox";
            enableCleanupCheckBox.Size = new Size(210, 24);
            enableCleanupCheckBox.TabIndex = 17;
            enableCleanupCheckBox.Text = "تفعيل\\تعطيل حذف السجلات";
            enableCleanupCheckBox.UseVisualStyleBackColor = true;
            enableCleanupCheckBox.CheckedChanged += checkBox2_CheckedChanged;
            // 
            // txtMaxRecords
            // 
            txtMaxRecords.Location = new Point(2, 306);
            txtMaxRecords.Name = "txtMaxRecords";
            txtMaxRecords.PlaceholderText = "Max records";
            txtMaxRecords.Size = new Size(178, 27);
            txtMaxRecords.TabIndex = 16;
            // 
            // txtKeepDays
            // 
            txtKeepDays.Location = new Point(2, 185);
            txtKeepDays.Name = "txtKeepDays";
            txtKeepDays.PlaceholderText = "Days to keep";
            txtKeepDays.Size = new Size(178, 27);
            txtKeepDays.TabIndex = 15;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(113, 272);
            label2.Name = "label2";
            label2.Size = new Size(110, 20);
            label2.TabIndex = 14;
            label2.Text = "أكبر عدد سجلات";
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(80, 146);
            label1.Name = "label1";
            label1.Size = new Size(152, 20);
            label1.TabIndex = 13;
            label1.Text = "عدد أيام حفظ السجلات";
            // 
            // checkBox1
            // 
            enableNotificationsCheckBox.AutoSize = true;
            enableNotificationsCheckBox.Name = "enableNotificationsCheckBox";
            enableNotificationsCheckBox.CheckState = CheckState.Checked;
            enableNotificationsCheckBox.Location = new Point(756, 50);
            enableNotificationsCheckBox.Size = new Size(223, 24);
            enableNotificationsCheckBox.TabIndex = 9;
            enableNotificationsCheckBox.Text = "تعطيل/تفعيل إشعارات الرسائل";
            enableNotificationsCheckBox.UseVisualStyleBackColor = true;
            enableNotificationsCheckBox.CheckedChanged += checkBox1_CheckedChanged;
            // 
            // dataGridView1
            // 
            dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView1.Location = new Point(6, 6);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.RowHeadersWidth = 51;
            dataGridView1.Size = new Size(728, 530);
            dataGridView1.TabIndex = 0;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(enableCleanupCheckBox);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(txtMaxRecords);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(txtKeepDays);
            groupBox1.Location = new Point(740, 144);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(266, 339);
            groupBox1.TabIndex = 18;
            groupBox1.TabStop = false;
            groupBox1.Text = "إعدادات حذف السجلات";
            // 
            // NotificationsForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1018, 628);
            Controls.Add(tabControl1);
            Name = "NotificationsForm";
            Text = "NotificationsForm";
            tabControl1.ResumeLayout(false);
            NotificationsTab.ResumeLayout(false);
            NotificationsTab.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        public TabPage NotificationsTab;
        public DataGridView dataGridView1;
        private CheckBox enableNotificationsCheckBox;
        private Label label2;
        private Label label1;
        private TextBox txtMaxRecords;
        private TextBox txtKeepDays;
        private CheckBox enableCleanupCheckBox;
        private GroupBox groupBox1;
    }
}