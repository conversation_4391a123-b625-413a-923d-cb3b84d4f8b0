using System.Globalization;

namespace Mis.Shared.Interface
{
    public static class SimpleLocalization
    {
        private static CultureInfo _currentCulture = new CultureInfo("en-US");
        private static readonly Dictionary<string, Dictionary<string, string>> _translations = new();

        public static event EventHandler<CultureChangedEventArgs> CultureChanged;

        public static CultureInfo CurrentCulture => _currentCulture;
        public static bool IsRightToLeft => _currentCulture.TextInfo.IsRightToLeft;

        static SimpleLocalization()
        {
            InitializeTranslations();
            LoadLanguagePreference();
        }

        private static void InitializeTranslations()
        {
            // English translations
            _translations["en-US"] = new Dictionary<string, string>
            {
                // Form Titles
                ["FormTitle"] = "MIS Agent",
                ["BarcodeFormTitle"] = "Barcode Configuration",
                ["PrintFormTitle"] = "Print Configuration",
                ["NotificationsFormTitle"] = "Notifications Management",
                ["PortFormTitle"] = "Port Configuration",
                // Tab Names
                ["BarcodeTabText"] = "Barcode Settings",
                ["PrintTabText"] = "Print Settings",
                ["PortTabText"] = "Port Settings",
                ["NotificationsTabText"] = "Notifications",

                // Labels
                ["BarcodeUrlLabel"] = "Barcode URL:",
                ["AgentUrlLabel"] = "Agent Url:",
                ["ComPortLabel"] = "COM Port:",
                ["AvailablePortsLabel"] = "Available Ports:",
                ["DefaultPrinterLabel"] = "Default Printer:",
                ["PrinterNameLabel"] = "Printer Name:",
                ["PaperType"] = "Paper Type:",

                // Buttons
                ["SaveBarcodeConfigButton"] = "Save Barcode Configuration",
                ["TestConnectionButton"] = "Test Connection",
                ["SavePrintConfigButton"] = "Save Print Configuration",
                ["Properties"] = "Properties",
                ["ClearNotificationsButton"] = "Clear All Notifications",
                ["EnableNotificationsCheckbox"] = "Enable/Disable Notifications",
                ["SwitchToArabic"] = "Switch to Arabic",
                ["SwitchToEnglish"] = "Switch to English",
                ["BtnSaveAllSettings.Text"] = "Save All Settings",

                // Messages
                ["LanguageSwitched"] = "Language Switched",
                ["LanguageSwitchedMessage"] = "Language has been switched successfully.",
                ["Error"] = "Error",
                ["Success"] = "Success",
                ["Warning"] = "Warning",
                ["Information"] = "Information",
                ["Confirmation"] = "Confirmation",
                ["Yes"] = "Yes",
                ["No"] = "No",
                ["OK"] = "OK",
                ["Cancel"] = "Cancel",
                ["Save"] = "Save",
                ["Close"] = "Close",

                // Error Messages
                ["InvalidUrlMessage"] = "Please enter a valid URL.",
                ["NoPortSelectedMessage"] = "Please select a COM port.",
                ["ConfigurationSavedMessage"] = "Configuration saved successfully!",
                ["ConfigurationFailedMessage"] = "Failed to save configuration. Please try again.",
                ["ConnectionSuccessMessage"] = "Connection test successful!",
                ["ConnectionFailedMessage"] = "Connection test failed. Please check your settings.",
                ["ClearNotificationsConfirmMessage"] = "Are you sure you want to clear all notifications?",
                ["NotificationsClearedMessage"] = "All notifications cleared successfully!",
                ["ClearNotificationsFailedMessage"] = "Failed to clear notifications.",
                ["ErrorOccurred"] = "An error occurred",

                // Tab names for main form
                ["TabBarcode"] = "Barcode",
                ["TabPrint"] = "Print",
                ["TabNotifications"] = "Notifications",
                ["TabPort"] = "Port",
                ["TabScanner"] = "Scanner",

                // Scanner form
                ["ScannerFormTitle"] = "Scanner Configuration",
                ["ScannerTabText"] = "Scanner Settings",
                ["AvailableScannersLabel"] = "Available Scanners:",
                ["UseBarcodeReaderCheckbox"] = "Use Barcode Reader",
                ["SaveScannerConfigButton"] = "Save Scanner Configuration",

                // Column headers
                ["NotificationIdColumn"] = "ID",
                ["NotificationNumberColumn"] = "Number",
                ["IsPrintedColumn"] = "Is Printed",
                ["ReceiveTimeColumn"] = "Receive Time"


                // Messages

            };

            // Arabic translations
            _translations["ar-SA"] = new Dictionary<string, string>
            {
                // Form Titles
                ["FormTitle"] = "وكيل نظام المعلومات الإدارية",
                ["BarcodeFormTitle"] = "إعدادات الباركود",
                ["PrintFormTitle"] = "إعدادات الطباعة",
                ["NotificationsFormTitle"] = "إدارة الإشعارات",
                ["PortFormTitle"] = "إدارة الإشعارات",

                // Tab Names
                ["BarcodeTabText"] = "إعدادات الباركود",
                ["PrintTabText"] = "إعدادات الطباعة",
                ["NotificationsTabText"] = "الإشعارات",
                ["PortTabText"] = "إعدادات المنفذ",

                // Labels
                ["BarcodeUrlLabel"] = "رابط الباركود:",
                ["AgentUrlLabel"] = "رابط التطبيق:",
                ["ComPortLabel"] = "منفذ COM:",
                ["AvailablePortsLabel"] = "المنافذ المتاحة:",
                ["DefaultPrinterLabel"] = "الطابعة الافتراضية:",
                ["PrinterNameLabel"] = "اسم الطابعة:",
                ["PaperType"] = "نوع الورق:",

                // Buttons
                ["SaveBarcodeConfigButton"] = "حفظ إعدادات الباركود",
                ["TestConnectionButton"] = "اختبار الاتصال",
                ["SavePrintConfigButton"] = "حفظ إعدادات الطباعة",
                ["Properties"] = "خصائص",
                ["ClearNotificationsButton"] = "مسح جميع الإشعارات",
                ["EnableNotificationsCheckbox"] = "تفعيل/إلغاء الإشعارات",
                ["SwitchToArabic"] = "التبديل إلى العربية",
                ["SwitchToEnglish"] = "التبديل إلى الإنجليزية",
                ["BtnSaveAllSettings.Text"] = "حفظ جميع الإعدادات",

                // Messages
                ["LanguageSwitched"] = "تم تبديل اللغة",
                ["LanguageSwitchedMessage"] = "تم تبديل اللغة بنجاح.",
                ["Error"] = "خطأ",
                ["Success"] = "نجح",
                ["Warning"] = "تحذير",
                ["Information"] = "معلومات",
                ["Confirmation"] = "تأكيد",
                ["Yes"] = "نعم",
                ["No"] = "لا",
                ["OK"] = "موافق",
                ["Cancel"] = "إلغاء",
                ["Save"] = "حفظ",
                ["Close"] = "إغلاق",

                // Error Messages
                ["InvalidUrlMessage"] = "يرجى إدخال رابط صحيح.",
                ["NoPortSelectedMessage"] = "يرجى اختيار منفذ COM.",
                ["ConfigurationSavedMessage"] = "تم حفظ الإعدادات بنجاح!",
                ["ConfigurationFailedMessage"] = "فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.",
                ["ConnectionSuccessMessage"] = "نجح اختبار الاتصال!",
                ["ConnectionFailedMessage"] = "فشل اختبار الاتصال. يرجى التحقق من الإعدادات.",
                ["ClearNotificationsConfirmMessage"] = "هل أنت متأكد من أنك تريد مسح جميع الإشعارات؟",
                ["NotificationsClearedMessage"] = "تم مسح جميع الإشعارات بنجاح!",
                ["ClearNotificationsFailedMessage"] = "فشل في مسح الإشعارات.",
                ["ErrorOccurred"] = "حدث خطأ",

                // Tab names for main form
                ["TabBarcode"] = "الباركود",
                ["TabPrint"] = "الطباعة",
                ["TabNotifications"] = "الإشعارات",
                ["TabPort"] = "المنفذ",
                ["TabScanner"] = "الماسح",

                // Scanner form
                ["ScannerFormTitle"] = "إعدادات الماسح",
                ["ScannerTabText"] = "إعدادات الماسح",
                ["AvailableScannersLabel"] = "الماسحات المتاحة:",
                ["UseBarcodeReaderCheckbox"] = "استخدام قارئ الباركود",
                ["SaveScannerConfigButton"] = "حفظ إعدادات الماسح",

                // Column headers
                ["NotificationIdColumn"] = "المعرف",
                ["NotificationNumberColumn"] = "الرقم",
                ["IsPrintedColumn"] = "مطبوع",
                ["ReceiveTimeColumn"] = "وقت الاستلام"
            };
        }

        public static void SetCulture(string cultureName)
        {
            var oldCulture = _currentCulture;
            var newCulture = new CultureInfo(cultureName);

            _currentCulture = newCulture;
            Thread.CurrentThread.CurrentCulture = newCulture;
            Thread.CurrentThread.CurrentUICulture = newCulture;
            CultureInfo.DefaultThreadCurrentCulture = newCulture;
            CultureInfo.DefaultThreadCurrentUICulture = newCulture;

            SaveLanguagePreference();

            // Fire culture changed event
            CultureChanged?.Invoke(null, new CultureChangedEventArgs
            {
                OldCulture = oldCulture,
                NewCulture = newCulture,
                IsRightToLeft = IsRightToLeft
            });
        }

        public static string GetString(string key, string defaultValue = null)
        {
            if (_translations.TryGetValue(_currentCulture.Name, out var translations) &&
                translations.TryGetValue(key, out var value))
            {
                return value;
            }

            return defaultValue ?? key;
        }

        private static void SaveLanguagePreference()
        {
            try
            {
                var settingsPath = Path.Combine(AppContext.BaseDirectory, "language.settings");
                File.WriteAllText(settingsPath, _currentCulture.Name);
            }
            catch
            {
                // Ignore save errors
            }
        }

        private static void LoadLanguagePreference()
        {
            try
            {
                var settingsPath = Path.Combine(AppContext.BaseDirectory, "language.settings");
                if (File.Exists(settingsPath))
                {
                    var cultureName = File.ReadAllText(settingsPath).Trim();
                    if (!string.IsNullOrEmpty(cultureName))
                    {
                        _currentCulture = new CultureInfo(cultureName);
                        Thread.CurrentThread.CurrentCulture = _currentCulture;
                        Thread.CurrentThread.CurrentUICulture = _currentCulture;
                        CultureInfo.DefaultThreadCurrentCulture = _currentCulture;
                        CultureInfo.DefaultThreadCurrentUICulture = _currentCulture;
                    }
                }
            }
            catch
            {
                // Ignore load errors, use default
            }
        }
    }

    public class CultureChangedEventArgs : EventArgs
    {
        public CultureInfo OldCulture { get; set; }
        public CultureInfo NewCulture { get; set; }
        public bool IsRightToLeft { get; set; }
    }
}
