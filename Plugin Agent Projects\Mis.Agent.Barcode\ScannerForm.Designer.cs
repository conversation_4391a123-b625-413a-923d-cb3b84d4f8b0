﻿namespace Mis.Agent.Barcode
{
    partial class ScannerForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            ScannerTab = new TabPage();
            checkBoxUseBarcodeReader = new CheckBox();
            //SaveScannerConfigurations = new Button();
            label7 = new Label();
            comboBoxScanners = new ComboBox();
            pictureScanned = new PictureBox();
            tabControl1.SuspendLayout();
            ScannerTab.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureScanned).BeginInit();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(ScannerTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(804, 452);
            tabControl1.TabIndex = 0;
            // 
            // ScannerTab
            // 
            ScannerTab.Controls.Add(checkBoxUseBarcodeReader);
           // ScannerTab.Controls.Add(SaveScannerConfigurations);
            ScannerTab.Controls.Add(label7);
            ScannerTab.Controls.Add(comboBoxScanners);
            ScannerTab.Controls.Add(pictureScanned);
            ScannerTab.Location = new Point(4, 29);
            ScannerTab.Name = "ScannerTab";
            ScannerTab.RightToLeft = RightToLeft.Yes;
            ScannerTab.Size = new Size(796, 419);
            ScannerTab.TabIndex = 0;
            ScannerTab.Text = "Scanner Tab";
            ScannerTab.UseVisualStyleBackColor = true;
            // 
            // checkBoxUseBarcodeReader
            // 
            checkBoxUseBarcodeReader.AutoSize = true;
            checkBoxUseBarcodeReader.Location = new Point(527, 44);
            checkBoxUseBarcodeReader.Name = "checkBoxUseBarcodeReader";
            checkBoxUseBarcodeReader.Size = new Size(257, 24);
            checkBoxUseBarcodeReader.TabIndex = 23;
            checkBoxUseBarcodeReader.Text = "مسح الصورة عن طريق قارئ الباركود";
            checkBoxUseBarcodeReader.UseVisualStyleBackColor = true;
            checkBoxUseBarcodeReader.CheckedChanged += checkBoxUseBarcodeReader_CheckedChanged;
            // 
            // SaveScannerConfigurations
            // 
            //SaveScannerConfigurations.Location = new Point(166, 10);
            //SaveScannerConfigurations.Name = "SaveScannerConfigurations";
            //SaveScannerConfigurations.Size = new Size(154, 35);
            //SaveScannerConfigurations.TabIndex = 22;
            //SaveScannerConfigurations.Text = "حفظ إعدادات ماسحة الصور";
            //SaveScannerConfigurations.UseVisualStyleBackColor = true;
            //SaveScannerConfigurations.Click += SaveScannerConfigurations_Click;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Location = new Point(572, 10);
            label7.Name = "label7";
            label7.Size = new Size(212, 20);
            label7.TabIndex = 21;
            label7.Text = "ماسحات الصور المتوفرة للاتصال";
            // 
            // comboBoxScanners
            // 
            comboBoxScanners.FormattingEnabled = true;
            comboBoxScanners.Location = new Point(341, 10);
            comboBoxScanners.Name = "comboBoxScanners";
            comboBoxScanners.Size = new Size(210, 28);
            comboBoxScanners.TabIndex = 20;
            // 
            // pictureScanned
            // 
            pictureScanned.BackgroundImageLayout = ImageLayout.Stretch;
            pictureScanned.BorderStyle = BorderStyle.FixedSingle;
            pictureScanned.Location = new Point(9, 76);
            pictureScanned.Margin = new Padding(4, 5, 4, 5);
            pictureScanned.Name = "pictureScanned";
            pictureScanned.Size = new Size(778, 331);
            pictureScanned.TabIndex = 19;
            pictureScanned.TabStop = false;
            // 
            // ScannerForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(tabControl1);
            Name = "ScannerForm";
            Text = "ScannerForm";
            tabControl1.ResumeLayout(false);
            ScannerTab.ResumeLayout(false);
            ScannerTab.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureScanned).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        public TabPage ScannerTab;
        private CheckBox checkBoxUseBarcodeReader;
       // private Button SaveScannerConfigurations;
        private Label label7;
        private ComboBox comboBoxScanners;
        public PictureBox pictureScanned;
    }
}