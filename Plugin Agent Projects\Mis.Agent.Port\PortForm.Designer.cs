﻿namespace Mis.Agent.Port
{
    partial class PortForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            PortTab = new TabPage();
            label4 = new Label();
            AgentProtocolField = new ComboBox();
            label2 = new Label();
            AgentIpField = new TextBox();
            label1 = new Label();
            AgentPortField = new TextBox();
            AgentUrlField = new TextBox();
            label3 = new Label();
            tabControl1.SuspendLayout();
            PortTab.SuspendLayout();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(PortTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(804, 453);
            tabControl1.TabIndex = 0;
            // 
            // PortTab
            // 
            PortTab.Controls.Add(label4);
            PortTab.Controls.Add(AgentProtocolField);
            PortTab.Controls.Add(label2);
            PortTab.Controls.Add(AgentIpField);
            PortTab.Controls.Add(label1);
            PortTab.Controls.Add(AgentPortField);
            PortTab.Controls.Add(AgentUrlField);
            PortTab.Controls.Add(label3);
            PortTab.Location = new Point(4, 29);
            PortTab.Name = "PortTab";
            PortTab.Padding = new Padding(3);
            PortTab.Size = new Size(796, 420);
            PortTab.TabIndex = 0;
            PortTab.Text = "Port Tab";
            PortTab.UseVisualStyleBackColor = true;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(420, 30);
            label4.Name = "label4";
            label4.Size = new Size(126, 20);
            label4.TabIndex = 12;
            label4.Text = "  بروتوكول التطبيق";
            // 
            // AgentProtocolField
            // 
            AgentProtocolField.DropDownStyle = ComboBoxStyle.DropDownList;
            AgentProtocolField.Items.AddRange(new object[] { "http", "https" });
            AgentProtocolField.Location = new Point(242, 23);
            AgentProtocolField.Name = "AgentProtocolField";
            AgentProtocolField.Size = new Size(133, 28);
            AgentProtocolField.TabIndex = 11;
            AgentProtocolField.SelectedIndexChanged += AgentProtocolField_SelectedIndexChanged;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(306, 114);
            label2.Name = "label2";
            label2.Size = new Size(73, 20);
            label2.TabIndex = 10;
            label2.Text = "التطبيق IP";
            // 
            // AgentIpField
            // 
            AgentIpField.Location = new Point(23, 111);
            AgentIpField.Name = "AgentIpField";
            AgentIpField.Size = new Size(196, 27);
            AgentIpField.TabIndex = 9;
            AgentIpField.TextChanged += AgentIpField_TextChanged;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(680, 114);
            label1.Name = "label1";
            label1.Size = new Size(91, 20);
            label1.TabIndex = 8;
            label1.Text = "منفذ التطبيق";
            // 
            // AgentPortField
            // 
            AgentPortField.Location = new Point(487, 107);
            AgentPortField.Name = "AgentPortField";
            AgentPortField.Size = new Size(102, 27);
            AgentPortField.TabIndex = 7;
            AgentPortField.TextChanged += PortTextBox_TextChanged;
            // 
            // AgentUrlField
            // 
            AgentUrlField.Location = new Point(242, 226);
            AgentUrlField.Name = "AgentUrlField";
            AgentUrlField.PlaceholderText = "http://localhost:";
            AgentUrlField.ReadOnly = true;
            AgentUrlField.Size = new Size(278, 27);
            AgentUrlField.TabIndex = 5;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Location = new Point(597, 226);
            label3.Name = "label3";
            label3.Size = new Size(97, 20);
            label3.TabIndex = 4;
            label3.Text = "عنوان التطبيق";
            // 
            // PortForm
            // 
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(tabControl1);
            Name = "PortForm";
            Text = "Port Form";
            tabControl1.ResumeLayout(false);
            PortTab.ResumeLayout(false);
            PortTab.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        public TabPage PortTab;
        private TextBox AgentUrlField;
        private Label label3;
        private Label label1;
        private TextBox AgentPortField;
        private Label label4;
        private ComboBox AgentProtocolField;
        private Label label2;
        private TextBox AgentIpField;
    }
}
