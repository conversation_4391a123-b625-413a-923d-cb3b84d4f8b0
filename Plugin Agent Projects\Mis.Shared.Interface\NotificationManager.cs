﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Shared.Interface
{
    public delegate void NotificationEventHandler(object sender, NotificationEventArgs e);

    public class NotificationEventArgs : EventArgs
    {
        public bool IsEnabled { get; set; }
    }

    public delegate void NotificationDataRequestHandler(object sender, NotificationDataEventArgs e);

    public class NotificationDataEventArgs : EventArgs
    {
        public IEnumerable<TransactionDto> Notifications { get; set; }
        public TaskCompletionSource<IEnumerable<TransactionDto>> CompletionSource { get; set; }
    }

    public static class NotificationManager
    {
        public static event NotificationEventHandler NotificationEvent;
        public static event Action NotificationDataUpdated;
        public static event Action NotificationCleanupRequested;
        public static event NotificationDataRequestHandler NotificationDataRequested;

        public static void Notify(NotificationEventArgs e)
        {
            NotificationEvent?.Invoke(null, e);
        }

        public static void NotifyDataUpdated()
        {
            NotificationDataUpdated?.Invoke();
        }

        public static void RequestCleanup()
        {
            NotificationCleanupRequested?.Invoke();
        }

        public static async Task<IEnumerable<TransactionDto>> RequestNotificationDataAsync()
        {
            var completionSource = new TaskCompletionSource<IEnumerable<TransactionDto>>();
            var eventArgs = new NotificationDataEventArgs
            {
                CompletionSource = completionSource
            };

            NotificationDataRequested?.Invoke(null, eventArgs);

            // Wait for the response with a timeout
            var timeoutTask = Task.Delay(5000); // 5 second timeout
            var completedTask = await Task.WhenAny(completionSource.Task, timeoutTask);

            if (completedTask == timeoutTask)
            {
                return new List<TransactionDto>(); // Return empty list on timeout
            }

            return await completionSource.Task;
        }
    }


}
