﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Mis.Agent.PluginSystem;
using Mis.Shared.Interface;
using System.Drawing.Printing;
using System.Reflection;

namespace Mis.Agent
{
    public class Startup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            var pluginManager = PluginManager.Instance;

            string pluginPath = Path.Combine(AppContext.BaseDirectory, "Plugins");

            // ⚠️ Register plugin controllers BEFORE MVC is built
            var mvcBuilder = services.AddControllers();

        
            pluginManager.RegisterControllers(mvcBuilder);
            pluginManager.RegisterServices(services); // optional DI registrations
         
            // Enable CORS with an "AllowAll" policy
            services.AddCors(options =>
            {
                options.AddPolicy("AllowAll", builder =>
                    builder.AllowAnyOrigin()
                           .AllowAnyMethod()
                           .AllowAnyHeader());
            });

            // Add SignalR support
            services.AddSignalR();
            services.AddTransient<AgentForm>();
            // In your host builder or Startup.ConfigureServices:
            services.AddSingleton<SerialPortManager>();

            // Configure Swagger for API documentation
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "My API", Version = "v1" });
            });
        }

       

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // Enable developer exception page for debugging
            app.UseDeveloperExceptionPage();

            // Serve static files
            app.UseStaticFiles();

            // Enable routing
            app.UseRouting();

            // Enable CORS
            app.UseCors("AllowAll");

            // Enable Authorization (if needed)
            app.UseAuthorization();
            // Configure endpoints
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers(); // Map controller routes
                endpoints.MapHub<ChatHub>("/chatHub"); // Map SignalR hub
            });

            // Enable Swagger
            app.UseSwagger();
            app.UseSwaggerUI(options =>
            {
                options.SwaggerEndpoint("/swagger/v1/swagger.json", "v1");
                options.RoutePrefix = string.Empty; // Swagger UI at root
            });
        }
    }
}
