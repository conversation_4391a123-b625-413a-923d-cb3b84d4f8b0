﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public interface IBarcodeAppService 
    {
        void PopulateCOMPorts();
        Task PublicInitialize(string baseUrl, string comPort, bool isCaptureImageMode);

        void SetBarcodeScannerPort();
        string[] GetAvailableCOMPorts();
        string GetConnectedBarcodePort();
        bool IsBarcodeScannerConnected(string portName);
        string GetBarcodeBaseUrl();
        void ShowNotification(string title, string text);
        string GetCOMPort();

    }
}
